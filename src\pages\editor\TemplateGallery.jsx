import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import TemplateCard from "@/components/ui/TemplateCard";
import EbookCardSkeleton from "@/components/ui/EbookCardSkeleton";
import useDataFetching from "@/hooks/useDataFetching";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

const Templates = () => {
  const { bookId } = useParams();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [perPage] = useState(10);
  const [allTemplates, setAllTemplates] = useState([]);
  const [hasMore, setHasMore] = useState(true);

  const { data: books, isLoading } = useDataFetching({
    queryKey: ["templates", page, perPage],
    endPoint: "/admin/templates",
    params: { page, per_page: perPage },
  });

  // Handle data accumulation for "Show More" functionality
  useEffect(() => {
    if (books?.data?.data) {
      if (page === 1) {
        // First page - replace all templates
        setAllTemplates(books.data.data);
      } else {
        // Subsequent pages - append to existing templates
        setAllTemplates(prev => [...prev, ...books.data.data]);
      }
      // Update hasMore based on pagination info
      setHasMore(page < books.data.total_pages);
    }
  }, [books, page]);

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  const handleUse = async (template) => {
    try {
      await api.post(`admin/ebooks/use-templates`, { ebook_id: bookId, template_id: template.id });

      // Invalidate all possible query patterns that might contain ebook data
      queryClient.invalidateQueries({ queryKey: ['ebooksData'] });
      // queryClient.invalidateQueries({ queryKey: ['ebook'] });
      // queryClient.invalidateQueries({ queryKey: ['single-ebook'] });

      // Show success message

      // Navigate back to editor
      navigate(`/ebooks/edit/${bookId}`);
    } catch (error) {
      console.error("Error using template:", error);
      toast.error("Failed to apply template. Please try again.");
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-700 mb-4">Select a Template</h2>

      <div className="grid grid-cols-4 gap-6">
        {isLoading && page === 1
          ? Array.from({ length: 5 }, (_, index) => <EbookCardSkeleton key={index} />)
          : allTemplates.length > 0 ? (
              allTemplates.map((template) => (
                <TemplateCard key={template.id} book={template} onUse={() => handleUse(template)} />
              ))
            ) : (
              <div className="col-span-full text-center text-gray-500">No templates found.</div>
            )}
      </div>

      {/* Show More Button */}
      {hasMore && allTemplates.length > 0 && (
        <div className="flex justify-center mt-8">
          <button
            onClick={handleLoadMore}
            disabled={isLoading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
          >
            {isLoading ? 'Loading...' : 'Show More'}
          </button>
        </div>
      )}
    </div>
  );
};

export default Templates;
